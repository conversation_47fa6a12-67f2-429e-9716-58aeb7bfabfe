import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { VitalService } from './vitals.service';
import { ApiResponseDTO, PaginationQueryDTO } from 'src/common/dtos';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { tbl_vital_measurements } from '@prisma/client';
import { CreateVitalDto, UpdateVitalDto } from './dto/vitals.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PERMISSIONS } from 'src/common/decorators/permissions.decorator';

@ApiTags('Vitals')
@ApiBearerAuth('access-token')
@Controller('vitals')
export class VitalController {
  constructor(private readonly vitalService: VitalService) { }

  @Get('encounter/:encounter_id')
  @PERMISSIONS('vitals.read.self', 'vitals.read.org')
  async getVitalsByEncounterId(
    @Param('encounter_id', ParseUUIDPipe) encounter_id: string,
  ): Promise<ApiResponseDTO<tbl_vital_measurements[]>> {
    return {
      data: await this.vitalService.getVitalByEncounterId(encounter_id),
    };
  }

  @Post('generate-vitals/:encounter_id')
  @PERMISSIONS('vitals.create.self', 'vitals.create.org')
  async generateVitalFromTranscription(
    @Param('encounter_id', ParseUUIDPipe) encounter_id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<any>> {
    return {
      data: await this.vitalService.generateVitalFromTranscription(
        encounter_id,
        userEmail,
      ),
    };
  }

  @Put('update-vital/:vital_measurement_id')
  @PERMISSIONS('vitals.update.self', 'vitals.update.org')
  async updateVital(
    @Param('vital_measurement_id', ParseUUIDPipe) vital_measurement_id: string,
    @Body() data: UpdateVitalDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<tbl_vital_measurements>> {
    return {
      data: await this.vitalService.updateVital(
        vital_measurement_id,
        data,
        userEmail,
      ),
    };
  }

  @Get('vital-types')
  async getAllVitals(@Query() pagination: PaginationQueryDTO) {
    const { page, limit } = pagination;
    return await this.vitalService.getVitalTypes(page, limit)
  }

  @Post('create-vitals')
  async createVitals(@Body() data: CreateVitalDto, @GetUserEmail() userEmail: string) {
    return await this.vitalService.createVitals(data, userEmail)
  }
}
