import { HttpStatus, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { VitalRepository } from "./vitals.repository";
import { TranscriptionsRepository } from "src/transcriptions/transcriptions.repository";
import { LLM_RESPONSETYPES, responseTypes, TRANSCRIPTION_TYPES } from "src/common/constants/common.constants";
import { UtilsService } from "src/common/utils/utils.service";
import { PrismaService } from "src/common/prisma/prisma.service";
import { CreateVitalDto, UpdateVitalDto, VitalMeasurementDto } from "./dto/vitals.dto";
import { tbl_vital_measurements } from "@prisma/client";

@Injectable()
export class VitalService {
  private readonly logger = new Logger(VitalService.name);
  constructor(
    private readonly vitalRepository: VitalRepository,
    private readonly transcriptionsRepository: TranscriptionsRepository,
    private readonly utilService: UtilsService,
    private readonly prismaService: PrismaService
  ) { }

  async getVitalByEncounterId(encounterId: string): Promise<tbl_vital_measurements[]> {
    return await this.vitalRepository.getVitalByEncounterId(encounterId)
  }

  //Generate Vitals from Transcription
  async generateVitalFromTranscription(
    encounter_id: string,
    userEmail: string,
  ): Promise<any> {
    try {
      // check if vitals already exist
      const existingVitals = await this.vitalRepository.getVitalByEncounterId(encounter_id);
      if (existingVitals.length > 0) {
        return { Vitals: existingVitals };
      }
      // get transcription
      const transcription = await this.transcriptionsRepository.getTranscriptionUsingEncounterId(encounter_id);

      if (!transcription) {
        this.logger.error(`No transcription found for encounter ID: ${encounter_id}`);
        throw new NotFoundException('No transcription found for this encounter');
      }

      const combinedTranscription = transcription
        .filter((tr) => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
        .map((tr) => tr.transcription)
        .filter((t) => t)
        .join(' ');
      // const sectionNames=allVitals.map(());
      const allVitals = await this.vitalRepository.getAllVitalsTypes();
      let vitalNames = allVitals.map((v) => v.display_name).join(', ');
      const prompt = `${combinedTranscription}

  You are a medical data extraction assistant.

  The valid vitals you can use are:
  ${vitalNames}

  Instructions:
  1. Only extract vitals that are explicitly mentioned in the transcript.
  2. For "Vital Name", use the closest match from the provided valid list (normalize synonyms like "BP" → "Blood Pressure – Systolic/Diastolic", "HR" → "Heart Rate (Pulse)", "O2" → "Oxygen Saturation (SpO₂)").
  3. For "Value", copy the exact numeric value and unit as spoken/written in the transcript. 
    - If no numeric value is given, set "Value" to null.
  4. Include "Measurement Time" only if explicitly mentioned (e.g., "at 10:30 AM").
  5. Respond ONLY with valid JSON. Do not include markdown, comments, or explanations.

  Output format:
  {
    "Vitals": [
      { "Vital Name": "<Vital from list>", "Value": "<value or null>", "Measurement Time": "<time if available>" }
    ]
  }

  If no vitals are mentioned in the transcript, return:
  { "Vitals": [] }
  `;

      const transcriptionIds = transcription
        .filter((tr) => tr.transcription_type === TRANSCRIPTION_TYPES.ORIGINAL)
        .map((tr) => tr.id);

      // ask LLM to extract vitals
      const vitalsData = await this.utilService.genereteResponseFromLLM(
        'gpt-4o',
        'user',
        prompt,
        1500,
        0.7,
        LLM_RESPONSETYPES.JSON,
        responseTypes.Vitals,
        transcriptionIds,
        userEmail,
      );
      if (!vitalsData) {
        this.logger.error(`Failed to generate vitals data from LLM for encounter ID: ${encounter_id}`);
        throw new Error('Error generating vitals from transcription');
      }

      let parsedJson = JSON.parse(vitalsData);
      let actualjson: any;

      if (this.utilService.IsParsableJson(parsedJson)) {
        actualjson = JSON.parse(parsedJson);
      } else {
        actualjson = parsedJson;
      }

      let vdata =
        actualjson['Vitals'] ||
        actualjson['vitals'] ||
        actualjson['Vitals Table'] ||
        actualjson['vitals table'];

      // Normalize helper
      function normalize(str: string) {
        return str.toLowerCase().replace(/[^a-z0-9 ]/g, '').trim();
      }

      //  Find matching vital(s)
      function findMatchingVital(vitalName: string) {
        const normName = normalize(vitalName);

        // exact match
        let match = allVitals.find(
          (v) => normalize(v.display_name) === normName,
        );
        if (match) return [match];

        // partial match
        const partialMatches = allVitals.filter((v) =>
          normalize(v.display_name).includes(normName),
        );
        if (partialMatches.length > 0) return partialMatches;

        return [];
      }

      // Map vitals from LLM → DB vitals
      let mappedVitals: any[] = [];

      if (vdata && vdata.length > 0) {
        for (const vital of vdata) {
          const matches = findMatchingVital(vital['Vital Name']);
          if (matches.length > 0) {
            for (const match of matches) {
              mappedVitals.push({
                vital_type_id: match.vital_type_id,
                vital_value: vital['Value'],
                measurement_time: transcription[0].created_at.toISOString(),
                encounter_id,
                created_by: 'Transcript',
              });
            }
          } else {
            this.logger.warn(
              `No DB match found for vital: ${vital['Vital Name']}`,
            );
          }
        }
      }
      // insert into DB
      let createVital: CreateVitalDto = {
        info: [...mappedVitals]
      }
      const createVitals = await this.vitalRepository.createVitals(createVital, userEmail);

      if (createVitals.length === 0) {
        this.logger.error(`Failed to create vitals in DB for encounter ID: ${encounter_id}`);
        throw new Error('Error creating vitals in db');
      }
      return { Vitals: createVitals }
    }
    catch (error) {
      this.logger.error(`Error generating vitals from transcription for encounter ID: ${encounter_id}:`, error?.stack);
      throw this.utilService.formatErrorResponse(
        error,
        'Failed to generate vitals from transcription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateVital(
    vital_measurement_id: string,
    data: UpdateVitalDto,
    userEmail: string,
  ): Promise<tbl_vital_measurements> {
    return await this.vitalRepository.updateVital(vital_measurement_id, data, userEmail);
  }

  async getVitalTypes(page:number,limit:number) {
    return await this.vitalRepository.getVitalTypesByPagination(page,limit)
  }

  async createVitals(data: CreateVitalDto, userEmail: string): Promise<tbl_vital_measurements[]> {
    return await this.vitalRepository.createVitals(data, userEmail)
  }
}